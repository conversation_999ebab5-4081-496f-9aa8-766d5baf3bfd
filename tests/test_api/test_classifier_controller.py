"""
Tests for the classifier controller.
"""
import json
import os
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import HTTPException
from fastapi.testclient import TestClient

from app.api.v1.controllers.ollie.classifier import query_classification
from app.models.schemas import ClassifyRequest, ClassifyResponse


@pytest.mark.asyncio
class TestClassifierController:
    """Test cases for the classifier controller."""

    @patch('app.api.v1.controllers.ollie.classifier.get_instructions')
    @patch('app.api.v1.controllers.ollie.classifier.classify_question')
    @patch('app.api.v1.controllers.ollie.classifier.extract_json_content')
    @patch('app.api.v1.controllers.ollie.classifier.cache')
    async def test_classify_simplrops_question_success(
        self, 
        mock_cache, 
        mock_extract_json, 
        mock_classify_question, 
        mock_get_instructions,
        mock_database,
        sample_classify_request
    ):
        """Test successful classification of SimplrOps question."""
        # Setup mocks
        mock_get_instructions.return_value = "Test instructions"
        mock_classify_question.return_value = '{"answer": "simplrops"}'
        mock_extract_json.return_value = {"answer": "simplrops"}
        mock_cache.get.return_value = []
        mock_cache.set.return_value = True
        
        # Mock simplrops_context
        with patch('app.api.v1.controllers.ollie.classifier.simplrops_context') as mock_simplrops_context, \
             patch('app.api.v1.controllers.ollie.classifier.correct_anchor_tags') as mock_correct_tags:
            
            mock_simplrops_context.return_value = "SimplrOps response"
            mock_correct_tags.return_value = "Corrected SimplrOps response"
            
            # Create mock request
            mock_request = MagicMock()
            mock_request.headers = {"userid": "test_user", "referer": "http://test.com"}
            
            # Create request object
            classify_request = ClassifyRequest(**sample_classify_request)
            
            # Call the function
            result = await query_classification(
                request=mock_request,
                classify_request=classify_request,
                database=mock_database
            )
            
            # Assertions
            assert isinstance(result, ClassifyResponse)
            assert result.success is True
            assert result.data == "Corrected SimplrOps response"
            
            # Verify mocks were called
            mock_get_instructions.assert_called()
            mock_classify_question.assert_called_once()
            mock_simplrops_context.assert_called_once()

    @patch('app.api.v1.controllers.ollie.classifier.get_instructions')
    @patch('app.api.v1.controllers.ollie.classifier.classify_question')
    @patch('app.api.v1.controllers.ollie.classifier.extract_json_content')
    @patch('app.api.v1.controllers.ollie.classifier.cache')
    async def test_classify_database_question_success(
        self, 
        mock_cache, 
        mock_extract_json, 
        mock_classify_question, 
        mock_get_instructions,
        mock_database,
        sample_classify_request
    ):
        """Test successful classification of database question."""
        # Setup mocks
        mock_get_instructions.return_value = "Test instructions"
        mock_classify_question.return_value = '{"database": "securityGroups_list", "chart_type": "table"}'
        mock_extract_json.return_value = {
            "database": "securityGroups_list", 
            "chart_type": "table"
        }
        mock_cache.get.return_value = []
        mock_cache.set.return_value = True
        
        # Create mock request
        mock_request = MagicMock()
        mock_request.headers = {"userid": "test_user", "referer": "http://test.com"}
        
        # Create request object
        classify_request = ClassifyRequest(prompt="Show me all security groups")
        
        # Call the function
        result = await query_classification(
            request=mock_request,
            classify_request=classify_request,
            database=mock_database
        )
        
        # Assertions
        assert isinstance(result, ClassifyResponse)
        assert result.success is True
        assert result.data["dbCollection"] == "securityGroups"
        assert result.data["responseType"] == "list"
        assert result.data["chart_type"] == "table"

    @patch('app.api.v1.controllers.ollie.classifier.get_instructions')
    @patch('app.api.v1.controllers.ollie.classifier.classify_question')
    @patch('app.api.v1.controllers.ollie.classifier.extract_json_content')
    @patch('app.api.v1.controllers.ollie.classifier.cache')
    async def test_classify_workday_question_success(
        self, 
        mock_cache, 
        mock_extract_json, 
        mock_classify_question, 
        mock_get_instructions,
        mock_database,
        sample_classify_request
    ):
        """Test successful classification of Workday question."""
        # Setup mocks
        mock_get_instructions.return_value = "Test instructions"
        mock_classify_question.return_value = '{"answer": "workday response"}'
        mock_extract_json.return_value = {"answer": "workday response"}
        mock_cache.get.return_value = []
        mock_cache.set.return_value = True
        
        # Create mock request
        mock_request = MagicMock()
        mock_request.headers = {"userid": "test_user", "referer": "http://test.com"}
        
        # Create request object
        classify_request = ClassifyRequest(prompt="How do I check my salary in Workday?")
        
        # Call the function
        result = await query_classification(
            request=mock_request,
            classify_request=classify_request,
            database=mock_database
        )
        
        # Assertions
        assert isinstance(result, ClassifyResponse)
        assert result.success is True
        assert result.data == "workday response"

    @patch('app.api.v1.controllers.ollie.classifier.get_instructions')
    @patch('app.api.v1.controllers.ollie.classifier.classify_question')
    @patch('app.api.v1.controllers.ollie.classifier.cache')
    async def test_classify_invalid_database_format_error(
        self,
        mock_cache,
        mock_classify_question,
        mock_get_instructions,
        mock_database,
        sample_classify_request
    ):
        """Test error handling for invalid database format."""
        # Setup mocks - use a database format without underscore to trigger validation error
        mock_get_instructions.return_value = "Test instructions"
        mock_classify_question.return_value = '{"database": "invalidformat", "chart_type": "table"}'
        mock_cache.get.return_value = []

        with patch('app.api.v1.controllers.ollie.classifier.extract_json_content') as mock_extract_json:
            mock_extract_json.return_value = {
                "database": "invalidformat",  # No underscore - this should trigger the error
                "chart_type": "table"
            }

            # Create mock request
            mock_request = MagicMock()
            mock_request.headers = {"userid": "test_user", "referer": "http://test.com"}

            # Create request object
            classify_request = ClassifyRequest(**sample_classify_request)

            # Call the function and expect HTTPException
            with pytest.raises(HTTPException) as exc_info:
                await query_classification(
                    request=mock_request,
                    classify_request=classify_request,
                    database=mock_database
                )

            assert exc_info.value.status_code == 400
            assert "Invalid database_type format" in str(exc_info.value.detail)

    @patch('app.api.v1.controllers.ollie.classifier.get_instructions')
    async def test_classify_get_instructions_error(
        self, 
        mock_get_instructions,
        mock_database,
        sample_classify_request
    ):
        """Test error handling when get_instructions fails."""
        # Setup mock to raise exception
        mock_get_instructions.side_effect = Exception("Database connection failed")
        
        # Create mock request
        mock_request = MagicMock()
        mock_request.headers = {"userid": "test_user", "referer": "http://test.com"}
        
        # Create request object
        classify_request = ClassifyRequest(**sample_classify_request)
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await query_classification(
                request=mock_request,
                classify_request=classify_request,
                database=mock_database
            )
        
        assert exc_info.value.status_code == 500
        assert exc_info.value.detail == "Internal Server Error"

    @patch('app.api.v1.controllers.ollie.classifier.get_instructions')
    @patch('app.api.v1.controllers.ollie.classifier.classify_question')
    @patch('app.api.v1.controllers.ollie.classifier.extract_json_content')
    @patch('app.api.v1.controllers.ollie.classifier.cache')
    async def test_classify_with_history(
        self, 
        mock_cache, 
        mock_extract_json, 
        mock_classify_question, 
        mock_get_instructions,
        mock_database,
        sample_classify_request
    ):
        """Test classification with existing user history."""
        # Setup mocks with history
        mock_get_instructions.return_value = "Test instructions"
        mock_classify_question.return_value = '{"answer": "simplrops"}'
        mock_extract_json.return_value = {"answer": "simplrops"}
        mock_cache.get.return_value = [
            ["Previous question", "Previous response"],
            ["Another question", "Another response"]
        ]
        mock_cache.set.return_value = True
        
        # Mock simplrops_context
        with patch('app.api.v1.controllers.ollie.classifier.simplrops_context') as mock_simplrops_context, \
             patch('app.api.v1.controllers.ollie.classifier.correct_anchor_tags') as mock_correct_tags:
            
            mock_simplrops_context.return_value = "SimplrOps response with history"
            mock_correct_tags.return_value = "Corrected SimplrOps response with history"
            
            # Create mock request
            mock_request = MagicMock()
            mock_request.headers = {"userid": "test_user", "referer": "http://test.com"}
            
            # Create request object
            classify_request = ClassifyRequest(**sample_classify_request)
            
            # Call the function
            result = await query_classification(
                request=mock_request,
                classify_request=classify_request,
                database=mock_database
            )
            
            # Assertions
            assert isinstance(result, ClassifyResponse)
            assert result.success is True
            assert result.data == "Corrected SimplrOps response with history"
            
            # Verify history was used
            mock_cache.get.assert_called_once_with("test_user")
            mock_cache.set.assert_called()


@pytest.mark.integration
class TestClassifierControllerIntegration:
    """Integration tests for classifier controller."""

    def test_classify_endpoint_authentication_required(self, client):
        """Test that classification endpoint requires authentication."""
        response = client.post(
            "/ollie/classify",
            json={"prompt": "What is SimplrOps?"}
        )
        # Should return 403 for missing authentication
        assert response.status_code == 403

    @patch.dict('os.environ', {
        'ENVIRONMENT_NAME': 'test',
        'AWS_REGION': 'us-east-1',
        'LOCAL_DB_URL': 'mongodb://localhost:27017/test_db'
    })
    @patch('app.core.config.get_ssm_parameters')
    @patch('app.services.auth_service.verify_jwt_token')
    def test_classify_endpoint_invalid_request_body(self, mock_verify_jwt, mock_ssm, authenticated_headers):
        """Test classification endpoint with invalid request body."""
        from fastapi.testclient import TestClient
        from app.server import app
        from app.database.database import get_database

        # Create a mock database function
        def mock_get_database():
            return MagicMock()

        # Override the dependency
        app.dependency_overrides[get_database] = mock_get_database

        # Mock SSM parameters for JWT verification and other config
        mock_ssm.return_value = {
            "jwtPublicKey": "test_public_key",
            "openaikey": "test_openai_key",
            "redispassword": "test_redis_password",
            "redishost": "localhost",
            "ai-model-endpoint": "https://test.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-15-preview",
            "embedding_azure_endpoint": "https://test.openai.azure.com/openai/deployments/text-embedding-3-small/embeddings?api-version=2023-05-15",
            "central/dburl": "mongodb://localhost:27017/test_db"
        }

        # Mock JWT verification to pass authentication
        mock_verify_jwt.return_value = {"sub": "1234567890", "name": "John Doe"}

        try:
            # Create a new client with the overridden dependencies
            test_client = TestClient(app)
            response = test_client.post(
                "/ollie/classify",
                headers=authenticated_headers,
                json={"invalid_field": "test"}
            )
            assert response.status_code == 422
        finally:
            # Clean up the dependency override
            app.dependency_overrides.clear()
