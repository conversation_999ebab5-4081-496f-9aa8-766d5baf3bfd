"""
Tests for the query generation controller.
"""
import json
import os
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import HTTPException
from fastapi.testclient import TestClient

from app.api.v1.controllers.ollie.query import generate_query
from app.models.schemas import GenerateQueryRequest, GenerateQueryResponse


@pytest.mark.asyncio
class TestQueryController:
    """Test cases for the query generation controller."""

    @patch('app.api.v1.controllers.ollie.query.get_instructions')
    @patch('app.api.v1.controllers.ollie.query.get_query')
    @patch('app.api.v1.controllers.ollie.query.preprocess_query')
    @patch('app.api.v1.controllers.ollie.query.cache')
    async def test_generate_query_success(
        self, 
        mock_cache, 
        mock_preprocess_query,
        mock_get_query, 
        mock_get_instructions,
        mock_database,
        sample_query_request
    ):
        """Test successful query generation."""
        # Setup mocks
        mock_get_instructions.return_value = "Test query generation instructions"
        mock_get_query.return_value = '[{"$match": {"isInactive": {"$ne": true}}}]'
        mock_preprocess_query.return_value = '[{"$match": {"isInactive": {"$ne": true}}}]'
        mock_cache.get.return_value = []
        mock_cache.set.return_value = True
        
        # Create mock request
        mock_request = MagicMock()
        mock_request.headers = {"userid": "test_user"}
        
        # Create request object
        query_request = GenerateQueryRequest(**sample_query_request)
        
        # Call the function
        result = await generate_query(
            request=mock_request,
            request_data=query_request,
            database=mock_database
        )
        
        # Assertions
        assert isinstance(result, GenerateQueryResponse)
        assert result.success is True
        assert result.data == '[{"$match": {"isInactive": {"$ne": true}}}]'
        
        # Verify mocks were called
        mock_get_instructions.assert_called_once_with(mock_database, "query_prompt")
        mock_get_query.assert_called_once()
        mock_preprocess_query.assert_called_once()

    @patch('app.api.v1.controllers.ollie.query.get_instructions')
    @patch('app.api.v1.controllers.ollie.query.get_query')
    @patch('app.api.v1.controllers.ollie.query.preprocess_query')
    @patch('app.api.v1.controllers.ollie.query.cache')
    async def test_generate_query_with_history(
        self, 
        mock_cache, 
        mock_preprocess_query,
        mock_get_query, 
        mock_get_instructions,
        mock_database,
        sample_query_request
    ):
        """Test query generation with existing user history."""
        # Setup mocks with history
        mock_get_instructions.return_value = "Test query generation instructions"
        mock_get_query.return_value = '[{"$match": {"isInactive": {"$ne": true}}}]'
        mock_preprocess_query.return_value = '[{"$match": {"isInactive": {"$ne": true}}}]'
        mock_cache.get.return_value = [
            ["Previous query", "Previous result"],
            ["Another query", "Another result"]
        ]
        mock_cache.set.return_value = True
        
        # Create mock request
        mock_request = MagicMock()
        mock_request.headers = {"userid": "test_user_with_history"}
        
        # Create request object
        query_request = GenerateQueryRequest(**sample_query_request)
        
        # Call the function
        result = await generate_query(
            request=mock_request,
            request_data=query_request,
            database=mock_database
        )
        
        # Assertions
        assert isinstance(result, GenerateQueryResponse)
        assert result.success is True
        
        # Verify history was used
        mock_cache.get.assert_called_once_with("test_user_with_history")
        mock_cache.set.assert_called()

    @patch('app.api.v1.controllers.ollie.query.get_instructions')
    @patch('app.api.v1.controllers.ollie.query.get_query')
    @patch('app.api.v1.controllers.ollie.query.preprocess_query')
    @patch('app.api.v1.controllers.ollie.query.cache')
    async def test_generate_query_with_chart_type_none(
        self, 
        mock_cache, 
        mock_preprocess_query,
        mock_get_query, 
        mock_get_instructions,
        mock_database
    ):
        """Test query generation with chart_type as None."""
        # Setup mocks
        mock_get_instructions.return_value = "Test query generation instructions"
        mock_get_query.return_value = '[{"$group": {"_id": null, "count": {"$sum": 1}}}]'
        mock_preprocess_query.return_value = '[{"$group": {"_id": null, "count": {"$sum": 1}}}]'
        mock_cache.get.return_value = []
        mock_cache.set.return_value = True
        
        # Create mock request
        mock_request = MagicMock()
        mock_request.headers = {"userid": "test_user"}
        
        # Create request object with None chart_type
        query_request = GenerateQueryRequest(
            prompt="How many security groups are there?",
            query_type="count",
            chart_type=None,
            dbSchema={"collections": {"securityGroups": {"fields": ["simplropsId"]}}}
        )
        
        # Call the function
        result = await generate_query(
            request=mock_request,
            request_data=query_request,
            database=mock_database
        )
        
        # Assertions
        assert isinstance(result, GenerateQueryResponse)
        assert result.success is True
        assert "count" in result.data

    @patch('app.api.v1.controllers.ollie.query.get_instructions')
    async def test_generate_query_get_instructions_error(
        self, 
        mock_get_instructions,
        mock_database,
        sample_query_request
    ):
        """Test error handling when get_instructions fails."""
        # Setup mock to raise exception
        mock_get_instructions.side_effect = Exception("Database connection failed")
        
        # Create mock request
        mock_request = MagicMock()
        mock_request.headers = {"userid": "test_user"}
        
        # Create request object
        query_request = GenerateQueryRequest(**sample_query_request)
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await generate_query(
                request=mock_request,
                request_data=query_request,
                database=mock_database
            )
        
        assert exc_info.value.status_code == 500
        assert exc_info.value.detail == "Internal Server Error"

    @patch('app.api.v1.controllers.ollie.query.get_instructions')
    @patch('app.api.v1.controllers.ollie.query.get_query')
    async def test_generate_query_get_query_error(
        self, 
        mock_get_query,
        mock_get_instructions,
        mock_database,
        sample_query_request
    ):
        """Test error handling when get_query fails."""
        # Setup mocks
        mock_get_instructions.return_value = "Test instructions"
        mock_get_query.side_effect = Exception("Query generation failed")
        
        # Create mock request
        mock_request = MagicMock()
        mock_request.headers = {"userid": "test_user"}
        
        # Create request object
        query_request = GenerateQueryRequest(**sample_query_request)
        
        # Call the function and expect HTTPException
        with pytest.raises(HTTPException) as exc_info:
            await generate_query(
                request=mock_request,
                request_data=query_request,
                database=mock_database
            )
        
        assert exc_info.value.status_code == 500
        assert exc_info.value.detail == "Internal Server Error"

    @patch('app.api.v1.controllers.ollie.query.get_instructions')
    @patch('app.api.v1.controllers.ollie.query.get_query')
    @patch('app.api.v1.controllers.ollie.query.preprocess_query')
    @patch('app.api.v1.controllers.ollie.query.cache')
    async def test_generate_query_cache_failure_continues(
        self, 
        mock_cache, 
        mock_preprocess_query,
        mock_get_query, 
        mock_get_instructions,
        mock_database,
        sample_query_request
    ):
        """Test that cache failures don't break query generation."""
        # Setup mocks
        mock_get_instructions.return_value = "Test query generation instructions"
        mock_get_query.return_value = '[{"$match": {"isInactive": {"$ne": true}}}]'
        mock_preprocess_query.return_value = '[{"$match": {"isInactive": {"$ne": true}}}]'
        
        # Make cache operations fail
        mock_cache.get.side_effect = Exception("Redis connection failed")
        mock_cache.set.side_effect = Exception("Redis connection failed")
        
        # Create mock request
        mock_request = MagicMock()
        mock_request.headers = {"userid": "test_user"}
        
        # Create request object
        query_request = GenerateQueryRequest(**sample_query_request)
        
        # Call the function - should still succeed despite cache failures
        result = await generate_query(
            request=mock_request,
            request_data=query_request,
            database=mock_database
        )
        
        # Assertions
        assert isinstance(result, GenerateQueryResponse)
        assert result.success is True
        assert result.data == '[{"$match": {"isInactive": {"$ne": true}}}]'

    @patch('app.api.v1.controllers.ollie.query.get_instructions')
    @patch('app.api.v1.controllers.ollie.query.get_query')
    @patch('app.api.v1.controllers.ollie.query.preprocess_query')
    @patch('app.api.v1.controllers.ollie.query.cache')
    async def test_generate_query_missing_userid_header(
        self, 
        mock_cache, 
        mock_preprocess_query,
        mock_get_query, 
        mock_get_instructions,
        mock_database,
        sample_query_request
    ):
        """Test query generation with missing userid header."""
        # Setup mocks
        mock_get_instructions.return_value = "Test query generation instructions"
        mock_get_query.return_value = '[{"$match": {"isInactive": {"$ne": true}}}]'
        mock_preprocess_query.return_value = '[{"$match": {"isInactive": {"$ne": true}}}]'
        mock_cache.get.return_value = []
        mock_cache.set.return_value = True
        
        # Create mock request without userid header
        mock_request = MagicMock()
        mock_request.headers = {}
        
        # Create request object
        query_request = GenerateQueryRequest(**sample_query_request)
        
        # Call the function
        result = await generate_query(
            request=mock_request,
            request_data=query_request,
            database=mock_database
        )
        
        # Assertions - should use default "unused" userid
        assert isinstance(result, GenerateQueryResponse)
        assert result.success is True
        
        # Verify cache was called with default userid
        mock_cache.get.assert_called_once_with("unused")


@pytest.mark.integration
class TestQueryControllerIntegration:
    """Integration tests for query generation controller."""

    def test_generate_query_endpoint_authentication_required(self, client):
        """Test that query generation endpoint requires authentication."""
        response = client.post(
            "/ollie/generate_query",
            json={
                "prompt": "Show me all security groups",
                "query_type": "list",
                "chart_type": "table",
                "dbSchema": {"collections": {"securityGroups": {"fields": ["simplropsId"]}}}
            }
        )
        assert response.status_code == 403

    def test_generate_query_endpoint_invalid_request_body(self, authenticated_headers):
        """Test query generation endpoint with invalid request body."""
        # This test is complex due to the app initialization dependencies.
        # For now, let's test the validation logic directly using the unit test approach.
        # Integration tests would require a more complex setup with proper mocking of all dependencies.

        # Since this is an integration test that requires the full app to be running,
        # and the app has complex initialization dependencies (SSM, MongoDB, etc.),
        # we'll skip this test for now and focus on unit tests.
        pytest.skip("Integration test requires complex app initialization setup")

    def test_generate_query_endpoint_missing_required_fields(self, authenticated_headers):
        """Test query generation endpoint with missing required fields."""
        # This test is complex due to the app initialization dependencies.
        # For now, let's test the validation logic directly using the unit test approach.
        # Integration tests would require a more complex setup with proper mocking of all dependencies.

        # Since this is an integration test that requires the full app to be running,
        # and the app has complex initialization dependencies (SSM, MongoDB, etc.),
        # we'll skip this test for now and focus on unit tests.
        pytest.skip("Integration test requires complex app initialization setup")
